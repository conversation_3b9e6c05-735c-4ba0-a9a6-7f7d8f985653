//! 题目相关的API模型
//!
//! 定义统一的题目API响应格式

use serde::Serialize;
use utoipa::ToSchema;
use recommendation_core::storage::dto::QuestionContent;

// 重新导出core模块的ApiQuestionContent，并添加ToSchema支持
pub use recommendation_core::infrastructure::dto::question::ApiQuestionContent as CoreApiQuestionContent;

/// 统一的API题目内容DTO（带OpenAPI支持）
/// 用于所有题目相关接口的响应格式
#[derive(Debug, Serialize, ToSchema, Clone)]
pub struct ApiQuestionContent {
    /// 题目ID
    pub id: String,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 难度
    pub difficulty: f64,
    /// 题目内容
    pub question_content: serde_json::Value,
    /// 选项
    pub options: serde_json::Value,
    /// 正确答案
    pub answer: serde_json::Value,
    /// 解析说明
    pub explanation: serde_json::Value,
    /// ELO评分
    pub elo_rating: f64,
    /// 使用次数
    pub usage_count: i32,
    /// 正确次数
    pub correct_count: i32,
    /// 所属题集
    #[serde(skip_serializing_if = "Option::is_none")]
    pub question_set: Option<String>,
    /// 链接
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
    /// 是否激活
    pub is_active: bool,
    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,
    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<String>,
    /// 小节ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub section_id: Option<i32>,
    /// 附加数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Value>,
    /// 模块内顺序（考试模块特有字段）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub module_sequence: Option<i32>,
}

impl ApiQuestionContent {
    /// 从core模块的ApiQuestionContent转换
    pub fn from_core(core_question: CoreApiQuestionContent) -> Self {
        Self {
            id: core_question.id,
            subject_id: core_question.subject_id,
            knowledge_id: core_question.knowledge_id,
            type_id: core_question.type_id,
            difficulty: core_question.difficulty,
            question_content: core_question.question_content,
            options: core_question.options,
            answer: core_question.answer,
            explanation: core_question.explanation,
            elo_rating: core_question.elo_rating,
            usage_count: core_question.usage_count,
            correct_count: core_question.correct_count,
            question_set: core_question.question_set,
            url: core_question.url,
            is_active: core_question.is_active,
            created_at: core_question.created_at,
            updated_at: core_question.updated_at,
            section_id: core_question.section_id,
            metadata: core_question.metadata,
            module_sequence: None, // core版本没有module_sequence
        }
    }

    /// 从 QuestionContent 转换为 ApiQuestionContent
    pub fn from_question_content(question: QuestionContent) -> Self {
        Self {
            id: question.id,
            subject_id: question.subject_id,
            knowledge_id: question.knowledge_id,
            type_id: question.type_id,
            difficulty: question.difficulty,
            question_content: question.question_content,
            options: question.options,
            answer: question.answer,
            explanation: question.explanation,
            elo_rating: question.elo_rating,
            usage_count: question.usage_count,
            correct_count: question.correct_count,
            question_set: question.question_set,
            url: question.url,
            is_active: question.is_active,
            created_at: Some(question.created_at.to_rfc3339()),
            updated_at: Some(question.updated_at.to_rfc3339()),
            section_id: question.section_id,
            metadata: question.metadata,
            module_sequence: None, // QuestionContent没有module_sequence
        }
    }

    /// 从题库服务的 QuestionDto 转换为 ApiQuestionContent
    pub fn from_question_dto(question: recommendation_core::application::question::question_bank_service::QuestionDto) -> Self {
        // 创建当前时间的 RFC3339 格式字符串
        let now = chrono::Utc::now().to_rfc3339();

        // 处理 answer 字段，确保格式与推荐接口一致
        // 推荐接口的 answer 格式为 {"identifier": "A"}
        let answer = if let Some(identifier) = question.answer.get("correct").and_then(|v| v.as_str()) {
            serde_json::json!({
                "identifier": identifier
            })
        } else {
            question.answer
        };

        // 处理 explanation 字段，确保格式与推荐接口一致
        // 推荐接口的 explanation 是一个数组，包含多个内容块
        let explanation = if let Some(explanation_value) = question.explanation {
            if let Some(text) = explanation_value.get("text").and_then(|v| v.as_str()) {
                // 将简单文本转换为推荐接口的复杂格式
                serde_json::json!([
                    {
                        "content": text,
                        "metadata": {
                            "content_type": "stem",
                            "style": ["p"]
                        },
                        "type": "string"
                    }
                ])
            } else {
                // 如果已经是数组格式，直接使用
                if explanation_value.is_array() {
                    explanation_value
                } else {
                    // 否则包装成数组
                    serde_json::json!([explanation_value])
                }
            }
        } else {
            // 默认空数组
            serde_json::json!([])
        };

        // 处理 options 字段，确保格式与推荐接口一致
        // 推荐接口的 options 是一个数组，每个选项包含内容和标识符
        let options = if let Some(options_value) = question.options {
            if options_value.is_object() && !options_value.is_array() {
                // 如果是简单的键值对对象，转换为数组格式
                let mut options_array = Vec::new();
                for (key, value) in options_value.as_object().unwrap() {
                    options_array.push(serde_json::json!({
                        "identifier": key,
                        "content": [{
                            "content": value,
                            "metadata": {
                                "content_type": "stem",
                                "style": []
                            },
                            "type": "string"
                        }],
                        "is_correct": key == answer.get("identifier").and_then(|v| v.as_str()).unwrap_or("")
                    }));
                }
                serde_json::json!(options_array)
            } else {
                // 如果已经是数组格式，直接使用
                options_value
            }
        } else {
            // 默认空数组
            serde_json::json!([])
        };

        Self {
            id: question.id,
            subject_id: question.subject_id,
            knowledge_id: question.knowledge_id,
            type_id: question.type_id,
            difficulty: question.difficulty as f64,
            question_content: question.content,
            options: options,
            answer: answer,
            explanation: explanation,
            elo_rating: question.elo_rating,
            usage_count: 0, // QuestionDto 没有这个字段，设为默认值
            correct_count: 0, // QuestionDto 没有这个字段，设为默认值
            question_set: None,
            url: None,
            is_active: true, // 默认为激活状态
            created_at: Some(now.clone()),
            updated_at: Some(now),
            section_id: None,
            metadata: None,
            module_sequence: None, // QuestionDto没有module_sequence
        }
    }
}

// 为了向后兼容，保持原有的 From 实现
impl From<QuestionContent> for ApiQuestionContent {
    fn from(question: QuestionContent) -> Self {
        Self::from_question_content(question)
    }
}
