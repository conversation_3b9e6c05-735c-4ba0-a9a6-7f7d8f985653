//! 考试会话API处理器
//!
//! 处理考试会话相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::Deserialize;
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    CreateExamSessionRequestDto,
    ResumeExamSessionRequestDto,
};
use recommendation_core::domain::exam::ExamType;
use recommendation_core::models::ErrorCode;
use crate::http::response;

/// 创建考试会话请求
#[derive(Debug, Deserialize)]
pub struct CreateSessionRequest {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
}

/// 恢复考试会话查询参数
#[derive(Debug, Deserialize)]
pub struct ResumeSessionQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 取消考试会话请求
#[derive(Debug, Deserialize)]
pub struct CancelSessionRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
}

/// 创建考试会话
///
/// POST /api/v1/exam/session/create
pub async fn create_session(
    request: web::Json<CreateSessionRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到创建考试会话请求: {:?}", request);

    // 构建应用服务请求
    let app_request = CreateExamSessionRequestDto {
        user_id: request.user_id,
        paper_id: request.paper_id,
        exam_type: request.exam_type,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.create_session(app_request).await {
        Ok(response) => {
            info!("成功创建考试会话: {}", response.session_id);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("创建考试会话失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("创建考试会话失败: {}", e)),
            ))
        }
    }
}

/// 恢复考试会话
///
/// GET /api/v1/exam/session/resume?user_id={user_id}
pub async fn resume_session(
    query: web::Query<ResumeSessionQuery>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到恢复考试会话请求，会话ID: {}, 用户ID: {}", query.session_id, query.user_id);

    // 构建应用服务请求
    let app_request = ResumeExamSessionRequestDto {
        session_id: query.session_id.clone(),
        user_id: query.user_id,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.resume_session(app_request).await {
        Ok(response) => {
            info!("成功恢复考试会话: {}", response.session_id);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("恢复考试会话失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("恢复考试会话失败: {}", e)),
            ))
        }
    }
}

/// 取消考试会话
///
/// POST /api/v1/exam/session/cancel
pub async fn cancel_session(
    request: web::Json<CancelSessionRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到取消考试会话请求: {:?}", request);

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.cancel_session(&request.session_id, request.user_id).await {
        Ok(_) => {
            info!("成功取消考试会话: {}", request.session_id);
            Ok(response::success("会话已取消"))
        }
        Err(e) => {
            error!("取消考试会话失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("取消考试会话失败: {}", e)),
            ))
        }
    }
}