//! 考试答题处理器
//!
//! 处理考试答题相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    SubmitAnswerRequestDto,
    SubmitExamRequestDto,
};
use recommendation_core::models::ErrorCode;
use crate::http::response;
use recommendation_core::domain::exam::value_objects::ModuleType;

/// 提交答案请求
#[derive(Debug, Deserialize)]
pub struct SubmitAnswerRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: String,
    /// 答题用时（秒）
    pub time_spent_seconds: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目在模块中的序号
    pub question_sequence: i32,
}

/// 提交答案响应
#[derive(Debug, Serialize)]
pub struct SubmitAnswerResponse {
    /// 是否提交成功
    pub success: bool,
    /// 是否正确
    pub is_correct: bool,
    /// 正确答案
    pub correct_answer: String,
    /// 解析说明
    pub explanation: Option<String>,
    /// 当前模块进度
    pub module_progress: ModuleProgressInfo,
}

/// 模块进度信息
#[derive(Debug, Serialize)]
pub struct ModuleProgressInfo {
    /// 已答题数
    pub answered_count: i32,
    /// 总题数
    pub total_count: i32,
    /// 正确题数
    pub correct_count: i32,
    /// 剩余时间（秒）
    pub remaining_time_seconds: i32,
}

/// 提交答案
///
/// POST /api/v1/exam/answer/submit
pub async fn submit_answer(
    request: web::Json<SubmitAnswerRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到提交答案请求: 会话ID={}, 题目ID={}, 用户答案={}",
          request.session_id, request.question_id, request.student_answer);

    // 构建应用服务请求
    let app_request = SubmitAnswerRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        question_id: request.question_id,
        student_answer: request.student_answer.clone(),
        time_spent_seconds: request.time_spent_seconds,
        module_type: request.module_type,
        question_sequence: request.question_sequence,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.submit_answer(app_request).await {
        Ok(response_dto) => {
            // 转换为API响应格式
            let response = SubmitAnswerResponse {
                success: response_dto.success,
                is_correct: response_dto.is_correct,
                correct_answer: response_dto.correct_answer,
                explanation: response_dto.explanation,
                module_progress: ModuleProgressInfo {
                    answered_count: response_dto.module_progress.answered_questions,
                    total_count: response_dto.module_progress.total_questions,
                    correct_count: response_dto.module_progress.correct_questions,
                    remaining_time_seconds: response_dto.module_progress.remaining_time_seconds.unwrap_or(0),
                },
            };

            info!("答案提交成功: 会话ID={}, 题目ID={}, 正确={}",
                  request.session_id, request.question_id, response.is_correct);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("答案提交失败: 会话ID={}, 题目ID={}, 错误: {}",
                   request.session_id, request.question_id, e);
            Ok(response::error_with_data(
                ErrorCode::InternalError,
                Some(format!("答案提交失败: {}", e)),
                serde_json::json!({}),
            ))
        }
    }
}

/// 获取答题历史请求参数
#[derive(Debug, Deserialize)]
pub struct GetAnswerHistoryQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 模块类型（可选）
    pub module_type: Option<ModuleType>,
}

/// 答题记录
#[derive(Debug, Serialize)]
pub struct AnswerRecord {
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题用时（秒）
    pub time_spent_seconds: Option<i32>,
    /// 答题时间
    pub answered_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目序号
    pub question_sequence: i32,
}

/// 获取答题历史响应
#[derive(Debug, Serialize)]
pub struct GetAnswerHistoryResponse {
    /// 会话ID
    pub session_id: String,
    /// 答题记录列表
    pub answers: Vec<AnswerRecord>,
    /// 总答题数
    pub total_answered: i32,
    /// 正确答题数
    pub total_correct: i32,
}

/// 获取答题历史
///
/// GET /api/v1/exam/answer/history?session_id={session_id}&user_id={user_id}
pub async fn get_answer_history(
    query: web::Query<GetAnswerHistoryQuery>,
    _exam_service: web::Data<dyn ExamApplicationService>,
) -> ActixResult<HttpResponse> {
    info!("收到获取答题历史请求: 会话ID={}, 用户ID={}", 
          query.session_id, query.user_id);

    // TODO: 实现答题历史查询逻辑
    // 这里需要：
    // 1. 验证会话权限
    // 2. 查询答题记录
    // 3. 统计答题情况
    // 4. 返回历史数据

    // 暂时返回模拟响应
    let response = GetAnswerHistoryResponse {
        session_id: query.session_id.clone(),
        answers: vec![], // TODO: 实际查询数据
        total_answered: 0,
        total_correct: 0,
    };

    info!("答题历史查询成功: 会话ID={}, 记录数={}",
          query.session_id, response.answers.len());
    Ok(response::success(response))
}



/// 提交考试请求参数
#[derive(Debug, Deserialize)]
pub struct SubmitExamRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否强制提交（即使未完成所有题目）
    pub force_submit: Option<bool>,
}

/// 提交整个考试
///
/// POST /api/v1/exam/submit
pub async fn submit_exam(
    request: web::Json<SubmitExamRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到考试提交请求: 会话ID={}, 用户ID={}, 强制提交={:?}",
          request.session_id, request.user_id, request.force_submit);

    // 构建应用服务请求
    let app_request = SubmitExamRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        force_submit: request.force_submit,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.submit_exam(app_request).await {
        Ok(response) => {
            info!("考试提交成功: 会话ID={}, 总分={}/{}, 正确率={:.1}%",
                  request.session_id,
                  response.exam_score.total_score,
                  response.exam_score.max_score,
                  response.statistics.accuracy_rate * 100.0);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("考试提交失败: 会话ID={}, 错误: {}", request.session_id, e);
            Ok(response::error_with_data(
                ErrorCode::InternalError,
                Some(format!("考试提交失败: {}", e)),
                serde_json::json!({}),
            ))
        }
    }
}
