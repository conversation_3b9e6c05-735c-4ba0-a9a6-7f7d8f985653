//! 增强版考试API处理器
//!
//! 提供优化后的考试接口，减少前端调用复杂度

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize};
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    ExamPrepareRequestDto, ModuleTransitionRequestDto, 
    EnhancedSubmitAnswerRequestDto, ComprehensiveStateRequestDto,
};
use recommendation_core::domain::exam::{ExamType, ModuleType};
use recommendation_core::models::ErrorCode;
use crate::http::response;

/// 考试准备请求
#[derive(Debug, Deserialize)]
pub struct PrepareExamRequest {
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 是否自动恢复未完成会话
    pub auto_resume: Option<bool>,
    /// 恢复的会话ID（可选）
    pub resume_session_id: Option<String>,
    /// 是否直接开始第一模块
    pub auto_start_first_module: Option<bool>,
    /// 是否包含用户历史记录
    pub include_user_history: Option<bool>,
}

/// 模块切换请求
#[derive(Debug, Deserialize)]
pub struct ModuleTransitionRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 操作类型
    pub action: String, // "submit_only", "start_next", "submit_and_next"
    /// 当前模块类型（如果需要提交）
    pub current_module_type: Option<ModuleType>,
    /// 下一模块类型（如果需要开始）
    pub next_module_type: Option<ModuleType>,
    /// 是否强制提交当前模块
    pub force_submit: Option<bool>,
}

/// 增强答题提交请求
#[derive(Debug, Deserialize)]
pub struct EnhancedSubmitAnswerRequest {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 题目ID
    pub question_id: i32,
    /// 学生答案
    pub student_answer: String,
    /// 答题用时（秒）
    pub time_spent_seconds: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 题目序号
    pub question_sequence: i32,
    /// 是否返回完整会话状态
    pub include_session_state: Option<bool>,
    /// 是否返回下一题信息
    pub include_next_question: Option<bool>,
}

/// 综合状态查询参数
#[derive(Debug, Deserialize)]
pub struct ComprehensiveStateQuery {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 是否包含答题历史
    pub include_answer_history: Option<bool>,
    /// 是否包含用户统计
    pub include_user_statistics: Option<bool>,
    /// 是否包含考试指引
    pub include_exam_guide: Option<bool>,
    /// 答题历史限制数量
    pub answer_history_limit: Option<i32>,
}

/// 考试准备接口
///
/// POST /api/v1/exam/prepare
pub async fn prepare_exam(
    request: web::Json<PrepareExamRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到考试准备请求: 用户ID={}, 试卷ID={}, 考试类型={:?}", 
          request.user_id, request.paper_id, request.exam_type);

    // 构建应用服务请求
    let app_request = ExamPrepareRequestDto {
        user_id: request.user_id,
        paper_id: request.paper_id,
        exam_type: request.exam_type,
        auto_resume: request.auto_resume,
        resume_session_id: request.resume_session_id.clone(),
        auto_start_first_module: request.auto_start_first_module,
        include_user_history: request.include_user_history,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.prepare_exam(app_request).await {
        Ok(response) => {
            info!("考试准备成功: 会话ID={}, 操作类型={:?}", 
                  response.session_info.session_id, response.action_type);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("考试准备失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("考试准备失败: {}", e)),
            ))
        }
    }
}

/// 模块切换接口
///
/// POST /api/v1/exam/module/transition
pub async fn transition_module(
    request: web::Json<ModuleTransitionRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到模块切换请求: 会话ID={}, 操作={}", 
          request.session_id, request.action);

    // 解析操作类型
    let action = match request.action.as_str() {
        "submit_only" => recommendation_core::application::exam::dto::ModuleTransitionAction::SubmitOnly,
        "start_next" => recommendation_core::application::exam::dto::ModuleTransitionAction::StartNext,
        "submit_and_next" => recommendation_core::application::exam::dto::ModuleTransitionAction::SubmitAndNext,
        _ => {
            error!("无效的操作类型: {}", request.action);
            return Ok(response::error_empty(
                ErrorCode::ValidationFailed,
                Some("无效的操作类型".to_string()),
            ));
        }
    };

    // 构建应用服务请求
    let app_request = ModuleTransitionRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        action,
        current_module_type: request.current_module_type,
        next_module_type: request.next_module_type,
        force_submit: request.force_submit,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.transition_module(app_request).await {
        Ok(response) => {
            info!("模块切换成功: 会话ID={}, 执行操作={:?}", 
                  request.session_id, response.executed_actions);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("模块切换失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("模块切换失败: {}", e)),
            ))
        }
    }
}

/// 增强答题提交接口
///
/// POST /api/v1/exam/answer/submit-enhanced
pub async fn submit_answer_enhanced(
    request: web::Json<EnhancedSubmitAnswerRequest>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到增强答题提交请求: 会话ID={}, 题目ID={}", 
          request.session_id, request.question_id);

    // 构建应用服务请求
    let app_request = EnhancedSubmitAnswerRequestDto {
        session_id: request.session_id.clone(),
        user_id: request.user_id,
        question_id: request.question_id,
        student_answer: request.student_answer.clone(),
        time_spent_seconds: request.time_spent_seconds,
        module_type: request.module_type,
        question_sequence: request.question_sequence,
        include_session_state: request.include_session_state,
        include_next_question: request.include_next_question,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.submit_answer_enhanced(app_request).await {
        Ok(response) => {
            info!("增强答题提交成功: 会话ID={}, 题目ID={}, 正确={}", 
                  request.session_id, request.question_id, 
                  response.answer_result.is_correct);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("增强答题提交失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("增强答题提交失败: {}", e)),
            ))
        }
    }
}

/// 获取综合状态接口
///
/// GET /api/v1/exam/state/comprehensive
pub async fn get_comprehensive_state(
    query: web::Query<ComprehensiveStateQuery>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到综合状态查询请求: 会话ID={}, 用户ID={}", 
          query.session_id, query.user_id);

    // 构建应用服务请求
    let app_request = ComprehensiveStateRequestDto {
        session_id: query.session_id.clone(),
        user_id: query.user_id,
        include_answer_history: query.include_answer_history,
        include_user_statistics: query.include_user_statistics,
        include_exam_guide: query.include_exam_guide,
        answer_history_limit: query.answer_history_limit,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.get_comprehensive_state(app_request).await {
        Ok(response) => {
            info!("综合状态查询成功: 会话ID={}, 模块数={}", 
                  query.session_id, response.module_details.len());
            Ok(response::success(response))
        }
        Err(e) => {
            error!("综合状态查询失败: {:?}", e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("综合状态查询失败: {}", e)),
            ))
        }
    }
}
