//! 用户状态API处理器
//!
//! 处理用户考试状态相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::Deserialize;
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    GetUserStatusRequestDto,
};
use recommendation_core::models::ErrorCode;
use crate::http::response;

/// 获取用户状态查询参数
#[derive(Debug, Deserialize)]
pub struct GetUserStatusQuery {
    /// 用户ID
    pub user_id: i64,
}

/// 获取用户状态
///
/// GET /api/v1/exam/user/status?user_id={user_id}
pub async fn get_user_status(
    query: web::Query<GetUserStatusQuery>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    info!("收到获取用户状态请求: 用户ID={}", query.user_id);

    // 构建应用服务请求
    let app_request = GetUserStatusRequestDto {
        user_id: query.user_id,
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.get_user_status(app_request).await {
        Ok(response) => {
            info!("成功获取用户状态: 用户ID={}, 历史记录数={}, 未完成会话数={}",
                  query.user_id, response.exam_history.len(), response.incomplete_sessions.len());
            Ok(response::success(response))
        }
        Err(e) => {
            error!("获取用户状态失败: 用户ID={}, 错误: {:?}", query.user_id, e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("获取用户状态失败: {}", e)),
            ))
        }
    }
}
