//! 题目相关数据传输对象
//!
//! 定义与题目交互的数据结构，包括:
//! - `QuestionDTO`: 基础的题目数据传输对象
//! - `QuestionContent`: 完整的题目内容传输对象
//! - `QuestionEloInfo`: 题目简要信息
//! - `QuestionMetadata`: 题目元数据
//! - 其他专用DTO

use chrono::{DateTime, Utc};

use serde::{Deserialize, Serialize};


/// 基础题目数据传输对象
/// 用于和API层交互的标准题目结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionDTO {
    /// 题目ID
    pub id: String,

    /// 学科ID
    pub subject_id: i32,

    /// 知识点ID
    pub knowledge_id: i32,

    /// 题型ID
    pub type_id: i32,

    /// 难度
    /// 这个是用来推算elo_rating的。不要在这个系统中使用！
    pub difficulty: f64,

    /// 题目内容
    pub content: serde_json::Value,

    /// 选项
    pub options: Option<serde_json::Value>,

    /// 正确答案
    pub answer: serde_json::Value,

    /// 解析说明
    pub explanation: Option<serde_json::Value>,

    /// ELO评分
    pub elo_rating: f64,

    /// 是否激活
    pub is_active: bool,
}

/// 统一的API题目内容DTO
/// 用于所有题目相关接口的响应格式，包括推荐系统和考试系统
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiQuestionContent {
    /// 题目ID
    pub id: String,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 难度
    pub difficulty: f64,
    /// 题目内容
    pub question_content: serde_json::Value,
    /// 选项
    pub options: serde_json::Value,
    /// 正确答案
    pub answer: serde_json::Value,
    /// 解析说明
    pub explanation: serde_json::Value,
    /// ELO评分
    pub elo_rating: f64,
    /// 使用次数
    pub usage_count: i32,
    /// 正确次数
    pub correct_count: i32,
    /// 所属题集
    #[serde(skip_serializing_if = "Option::is_none")]
    pub question_set: Option<String>,
    /// 链接
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
    /// 是否激活
    pub is_active: bool,
    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<String>,
    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<String>,
    /// 小节ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub section_id: Option<i32>,
    /// 附加数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Value>,
    /// 模块内顺序（考试模块特有字段）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub module_sequence: Option<i32>,
}

impl ApiQuestionContent {
    /// 从 QuestionContent 转换为 ApiQuestionContent
    pub fn from_question_content(question: QuestionContent) -> Self {
        Self {
            id: question.id,
            subject_id: question.subject_id,
            knowledge_id: question.knowledge_id,
            type_id: question.type_id,
            difficulty: question.difficulty,
            question_content: question.question_content,
            options: question.options,
            answer: question.answer,
            explanation: question.explanation,
            elo_rating: question.elo_rating,
            usage_count: question.usage_count,
            correct_count: question.correct_count,
            question_set: question.question_set,
            url: question.url,
            is_active: question.is_active,
            created_at: Some(question.created_at.to_rfc3339()),
            updated_at: Some(question.updated_at.to_rfc3339()),
            section_id: question.section_id,
            metadata: question.metadata,
            module_sequence: None, // QuestionContent没有module_sequence
        }
    }

    /// 从考试系统的 QuestionContentDetail 创建 ApiQuestionContent
    pub fn from_exam_question_detail(
        detail: &crate::domain::exam::repository::QuestionContentDetail,
        module_sequence: i32,
        include_answers: bool,
    ) -> Self {
        Self {
            id: detail.question_id.to_string(), // 转换ID类型
            subject_id: detail.subject_id,
            knowledge_id: detail.knowledge_id,
            type_id: detail.type_id,
            difficulty: detail.difficulty as f64,
            question_content: detail.question_content.clone(), // 使用正确的字段名
            options: detail.options.clone().unwrap_or(serde_json::Value::Array(vec![])),
            answer: if include_answers { detail.answer.clone() } else { serde_json::Value::Null },
            explanation: if include_answers {
                detail.explanation.clone().unwrap_or(serde_json::Value::Array(vec![]))
            } else {
                serde_json::Value::Array(vec![])
            },
            elo_rating: detail.elo_rating,
            usage_count: detail.usage_count,
            correct_count: detail.correct_count,
            question_set: detail.question_set.clone(),
            url: detail.url.clone(),
            is_active: detail.is_active,
            created_at: Some(detail.created_at.to_rfc3339()),
            updated_at: Some(detail.updated_at.to_rfc3339()),
            section_id: detail.section_id,
            metadata: None,
            module_sequence: Some(module_sequence), // 保留考试特有的模块序号
        }
    }
}

/// 题目内容
/// 完整的题目内容传输对象，包含全部题目相关字段
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionContent {
    /// 题目ID
    pub id: String,

    /// 学科ID
    pub subject_id: i32,

    /// 知识点ID
    pub knowledge_id: i32,

    /// 题型ID
    pub type_id: i32,

    /// 难度
    /// 这个是用来推算elo_rating的。不要在这个系统中使用！
    pub difficulty: f64,

    /// 题目内容
    pub question_content: serde_json::Value,

    /// 选项
    pub options: serde_json::Value,

    /// 正确答案
    pub answer: serde_json::Value,

    /// 解析说明
    pub explanation: serde_json::Value,

    /// ELO评分
    pub elo_rating: f64,

    /// 使用次数
    pub usage_count: i32,

    /// 正确次数
    pub correct_count: i32,

    /// 所属题集
    #[serde(skip_serializing)]
    pub question_set: Option<String>,

    /// 链接
    #[serde(skip_serializing)]
    pub url: Option<String>,

    /// 是否激活
    pub is_active: bool,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,

    /// 小节ID
    pub section_id: Option<i32>,

    /// 附加数据
    pub metadata: Option<serde_json::Value>,
}

/// 题目ELO信息
/// 专门用于ELO推荐系统，只包含ELO相关的必要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionEloInfo {
    /// 题目ID
    pub question_id: String,

    /// 知识点ID
    pub knowledge_id: i32,

    /// ELO评分
    pub elo_rating: f64,
}

/// IRT参数
/// 题目项目反应理论参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IrtParams {
    /// 难度参数(b)
    pub difficulty: f64,

    /// 区分度参数(a)
    pub discrimination: f64,

    /// 猜测参数(c)
    pub guessing: f64,
}





/// 题目元数据 - 用于批量查询，避免N+1查询问题
/// 包含推荐算法等常用的元数据，但不包含完整内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionMetadata {
    /// 题目ID
    pub id: String,

    /// 学科ID
    pub subject_id: i32,

    /// 知识点ID
    pub knowledge_id: i32,

    /// 知识点名称
    pub knowledge_name: Option<String>,

    /// 题型ID
    pub type_id: i32,

    /// 难度
    pub difficulty: f64,

    /// ELO评分
    pub elo_rating: Option<f64>,

    /// 正确率
    pub correct_ratio: Option<f64>,
}




// 转换方法实现
impl From<QuestionMetadata> for QuestionEloInfo {
    fn from(meta: QuestionMetadata) -> Self {
        Self {
            question_id: meta.id,
            knowledge_id: meta.knowledge_id,
            elo_rating: meta.elo_rating.unwrap_or(1700.0),
        }
    }
}
