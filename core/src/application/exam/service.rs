//! 考试应用服务接口
//!
//! 定义考试应用服务的接口，遵循DDD架构原则

use async_trait::async_trait;
use crate::error::Result;

use super::dto::{
    CreateExamSessionRequestDto, CreateExamSessionResponseDto,
    ResumeExamSessionRequestDto, ResumeExamSessionResponseDto,
    StartModuleRequestDto, StartModuleResponseDto,
    SubmitAnswerRequestDto, SubmitAnswerResponseDto,
    SubmitModuleRequestDto, SubmitModuleResponseDto,
    SubmitExamRequestDto, SubmitExamResponseDto,
    GetUserStatusRequestDto, GetUserStatusResponseDto,
    ExamGuideRequestDto, ExamGuideResponseDto,
    ExamStateDto,
    // 新增优化接口的DTO
    ExamPrepareRequestDto, ExamPrepareResponseDto,
    ModuleTransitionRequestDto, ModuleTransitionResponseDto,
    EnhancedSubmitAnswerRequestDto, EnhancedSubmitAnswerResponseDto,
    ComprehensiveStateRequestDto, ComprehensiveStateResponseDto,
};

/// 考试应用服务接口
/// 
/// 提供考试相关的高级功能，协调领域服务完成用例
#[async_trait]
pub trait ExamApplicationService: Send + Sync {
    /// 创建考试会话
    /// 
    /// # 参数
    /// * `request` - 创建会话请求
    /// 
    /// # 返回
    /// 返回创建的会话信息和配置
    /// 
    /// # 业务规则
    /// - 用户同时只能有一个进行中的考试会话
    /// - 试卷必须存在且可用
    /// - 根据考试类型初始化相应的模块
    async fn create_session(
        &self,
        request: CreateExamSessionRequestDto,
    ) -> Result<CreateExamSessionResponseDto>;

    /// 恢复考试会话
    ///
    /// # 参数
    /// * `request` - 恢复会话请求
    ///
    /// # 返回
    /// 返回会话状态和进度信息
    ///
    /// # 业务规则
    /// - 查找用户进行中的会话
    /// - 返回当前状态和剩余时间
    /// - 支持从任意中断点恢复
    async fn resume_session(
        &self,
        request: ResumeExamSessionRequestDto,
    ) -> Result<ResumeExamSessionResponseDto>;

    /// 取消考试会话
    ///
    /// # 参数
    /// * `session_id` - 会话ID
    /// * `user_id` - 用户ID
    ///
    /// # 返回
    /// 返回操作结果
    ///
    /// # 业务规则
    /// - 只能取消属于该用户的会话
    /// - 只能取消进行中的会话
    /// - 取消后会话状态变为已取消
    async fn cancel_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<()>;

    /// 开始模块
    /// 
    /// # 参数
    /// * `request` - 开始模块请求
    /// 
    /// # 返回
    /// 返回模块信息和题目列表
    /// 
    /// # 业务规则
    /// - 验证模块开始的前置条件
    /// - 根据考试类型筛选题目
    /// - 初始化答题记录
    /// - 开始计时
    async fn start_module(
        &self,
        request: StartModuleRequestDto,
    ) -> Result<StartModuleResponseDto>;

    /// 获取会话状态
    /// 
    /// # 参数
    /// * `session_id` - 会话ID
    /// * `user_id` - 用户ID
    /// 
    /// # 返回
    /// 返回详细的会话状态信息
    async fn get_session_state(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<ExamStateDto>;

    /// 检查会话有效性
    ///
    /// # 参数
    /// * `session_id` - 会话ID
    /// * `user_id` - 用户ID
    ///
    /// # 返回
    /// 返回会话是否有效
    ///
    /// # 业务规则
    /// - 会话必须存在
    /// - 会话必须属于指定用户
    /// - 会话必须处于进行中状态
    async fn validate_session(
        &self,
        session_id: &str,
        user_id: i64,
    ) -> Result<bool>;

    /// 提交答案
    ///
    /// # 参数
    /// * `request` - 提交答案请求
    ///
    /// # 返回
    /// 返回答题结果和反馈信息
    ///
    /// # 业务规则
    /// - 验证会话和题目有效性
    /// - 记录答题结果和用时
    /// - 更新模块进度
    /// - 计算答题正确性
    /// - 提供即时反馈
    async fn submit_answer(
        &self,
        request: SubmitAnswerRequestDto,
    ) -> Result<SubmitAnswerResponseDto>;

    /// 提交模块
    ///
    /// # 参数
    /// * `request` - 提交模块请求
    ///
    /// # 返回
    /// 返回模块成绩和下一步操作信息
    ///
    /// # 业务规则
    /// - 验证会话有效性和用户权限
    /// - 验证模块可以提交（已开始且未提交）
    /// - 计算模块成绩和统计信息
    /// - 更新模块状态为已提交
    /// - 如果是第一模块，触发自适应逻辑确定第二模块类型
    /// - 如果是最后一个模块，准备考试完成
    /// - 返回成绩、统计和下一步操作建议
    /// - 支持强制提交（未完成所有题目）
    async fn submit_module(
        &self,
        request: SubmitModuleRequestDto,
    ) -> Result<SubmitModuleResponseDto>;

    /// 提交整个考试
    ///
    /// # 参数
    /// * `request` - 提交考试请求
    ///
    /// # 返回
    /// 返回考试成绩和统计信息
    ///
    /// # 业务规则
    /// - 验证会话有效性和用户权限
    /// - 计算各学科成绩和总分
    /// - 生成考试统计报告
    /// - 更新会话状态为已完成
    /// - 记录考试完成时间
    /// - 支持强制提交（未完成所有题目）
    async fn submit_exam(
        &self,
        request: SubmitExamRequestDto,
    ) -> Result<SubmitExamResponseDto>;

    /// 获取用户状态
    ///
    /// # 参数
    /// * `request` - 用户状态请求
    ///
    /// # 返回
    /// 返回用户的考试历史和未完成会话信息
    ///
    /// # 业务规则
    /// - 查询用户的历史考试记录
    /// - 查询用户的未完成会话
    /// - 计算用户的考试统计信息
    /// - 按时间倒序返回历史记录
    async fn get_user_status(
        &self,
        request: GetUserStatusRequestDto,
    ) -> Result<GetUserStatusResponseDto>;

    /// 获取考试指引
    ///
    /// # 参数
    /// * `request` - 考试指引请求
    ///
    /// # 返回
    /// 返回考试指引信息和新创建的会话ID
    ///
    /// # 业务规则
    /// - 创建新的考试会话
    /// - 根据考试类型返回相应的指引信息
    /// - 包含时间分配、题目分布等信息
    /// - 提供考试说明和注意事项
    async fn get_exam_guide(
        &self,
        request: ExamGuideRequestDto,
    ) -> Result<ExamGuideResponseDto>;

    // ==================== 优化接口 ====================

    /// 考试准备（合并接口）
    ///
    /// # 参数
    /// * `request` - 考试准备请求
    ///
    /// # 返回
    /// 返回用户状态、考试指引、会话信息和第一模块题目
    ///
    /// # 业务规则
    /// - 获取用户状态和历史记录
    /// - 检查是否有未完成会话，支持自动恢复
    /// - 创建新会话或恢复现有会话
    /// - 返回考试指引信息
    /// - 可选择直接开始第一模块
    /// - 一次调用完成考试准备的所有步骤
    async fn prepare_exam(
        &self,
        request: ExamPrepareRequestDto,
    ) -> Result<ExamPrepareResponseDto>;

    /// 模块切换（合并接口）
    ///
    /// # 参数
    /// * `request` - 模块切换请求
    ///
    /// # 返回
    /// 返回模块提交结果、下一模块信息和完整考试状态
    ///
    /// # 业务规则
    /// - 提交当前模块（如果需要）
    /// - 计算模块成绩和统计
    /// - 触发自适应逻辑（如果是第一模块）
    /// - 自动开始下一模块（如果有）
    /// - 返回完整的考试状态
    /// - 原子操作，确保数据一致性
    async fn transition_module(
        &self,
        request: ModuleTransitionRequestDto,
    ) -> Result<ModuleTransitionResponseDto>;

    /// 增强答题提交
    ///
    /// # 参数
    /// * `request` - 增强答题提交请求
    ///
    /// # 返回
    /// 返回答题结果、模块进度和可选的完整会话状态
    ///
    /// # 业务规则
    /// - 提交答案并记录结果
    /// - 更新模块进度
    /// - 可选返回完整会话状态（减少额外查询）
    /// - 提供下一步操作建议
    /// - 支持批量状态更新
    async fn submit_answer_enhanced(
        &self,
        request: EnhancedSubmitAnswerRequestDto,
    ) -> Result<EnhancedSubmitAnswerResponseDto>;

    /// 获取综合状态
    ///
    /// # 参数
    /// * `request` - 综合状态请求
    ///
    /// # 返回
    /// 返回完整的考试状态、用户信息和历史记录
    ///
    /// # 业务规则
    /// - 获取当前会话的完整状态
    /// - 包含所有模块进度信息
    /// - 可选包含答题历史
    /// - 可选包含用户统计信息
    /// - 可选包含考试指引
    /// - 一次调用获取所有相关状态
    async fn get_comprehensive_state(
        &self,
        request: ComprehensiveStateRequestDto,
    ) -> Result<ComprehensiveStateResponseDto>;

}
