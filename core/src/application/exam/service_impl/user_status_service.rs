//! 用户状态服务实现
//!
//! 负责处理用户考试状态查询、历史记录获取等功能

use tracing::info;

use crate::error::Result;
use crate::domain::exam::{
    ExamSessionRepository, ExamAnswerRepository, SatPaperRepository,
    ExamSession, SessionStatus, ExamType,
};

use super::super::dto::{
    GetUserStatusRequestDto, GetUserStatusResponseDto,
    ExamHistoryDto, IncompleteSessionDto, UserExamStatisticsDto,
    SessionProgressDto,
};

/// 用户状态服务实现
#[derive(Clone)]
pub struct UserStatusServiceImpl;

impl UserStatusServiceImpl {
    pub fn new() -> Self {
        Self
    }

    /// 获取用户状态
    pub async fn get_user_status(
        &self,
        request: GetUserStatusRequestDto,
        session_repository: &dyn ExamSessionRepository,
        answer_repository: &dyn ExamAnswerRepository,
        paper_repository: &dyn SatPaperRepository,
    ) -> Result<GetUserStatusResponseDto> {
        info!("获取用户状态: 用户ID={}", request.user_id);

        // 1. 获取用户的所有会话记录
        let all_sessions = session_repository
            .find_history_by_user_id(request.user_id, Some(50))
            .await?;

        // 2. 分离已完成和未完成的会话
        let mut completed_sessions = Vec::new();
        let mut incomplete_sessions = Vec::new();

        for session in all_sessions {
            match session.session_status {
                SessionStatus::Completed => completed_sessions.push(session),
                SessionStatus::InProgress => incomplete_sessions.push(session),
            }
        }

        // 3. 构建考试历史记录
        let exam_history = self.build_exam_history(completed_sessions, paper_repository).await?;

        // 4. 构建未完成会话信息
        let incomplete_session_dtos = self.build_incomplete_sessions(incomplete_sessions, answer_repository).await?;

        // 5. 计算统计信息
        let statistics = self.calculate_user_statistics(&exam_history, &incomplete_session_dtos).await?;

        // 6. 构建响应
        let response = GetUserStatusResponseDto {
            user_id: request.user_id,
            exam_history,
            incomplete_sessions: incomplete_session_dtos,
            statistics,
        };

        info!("用户状态获取成功: 用户ID={}, 历史记录数={}, 未完成会话数={}",
              request.user_id, response.exam_history.len(), response.incomplete_sessions.len());
        Ok(response)
    }

    /// 构建考试历史记录
    async fn build_exam_history(
        &self,
        sessions: Vec<ExamSession>,
        paper_repository: &dyn SatPaperRepository,
    ) -> Result<Vec<ExamHistoryDto>> {
        let mut history = Vec::new();

        for session in sessions {
            // 获取试卷信息
            let paper = paper_repository
                .find_paper_by_id(session.paper_id)
                .await?;

            let paper_name = paper
                .map(|p| p.paper_name)
                .unwrap_or_else(|| format!("试卷 {}", session.paper_id));

            // TODO: 从模考历史表获取实际分数数据
            // 这里暂时使用模拟数据
            let history_dto = ExamHistoryDto {
                session_id: session.session_id.clone(),
                paper_name,
                exam_type: session.exam_type,
                total_score: None, // TODO: 从 t_sat_mock_exam_history 获取
                reading_score: None,
                math_score: None,
                accuracy_rate: None,
                total_time_seconds: Some(session.total_time_seconds),
                exam_status: "completed".to_string(),
                completed_at: session.completed_at,
            };

            history.push(history_dto);
        }

        // 按完成时间倒序排列
        history.sort_by(|a, b| {
            match (a.completed_at, b.completed_at) {
                (Some(a_time), Some(b_time)) => b_time.cmp(&a_time),
                (Some(_), None) => std::cmp::Ordering::Less,
                (None, Some(_)) => std::cmp::Ordering::Greater,
                (None, None) => std::cmp::Ordering::Equal,
            }
        });

        Ok(history)
    }

    /// 构建未完成会话信息
    async fn build_incomplete_sessions(
        &self,
        sessions: Vec<ExamSession>,
        answer_repository: &dyn ExamAnswerRepository,
    ) -> Result<Vec<IncompleteSessionDto>> {
        let mut incomplete = Vec::new();

        for session in sessions {
            // 从答题记录计算进度
            let (total_answered, _total_correct, _) = answer_repository
                .count_answers_by_session(&session.session_id)
                .await
                .unwrap_or((0, 0, 0));

            let total_questions = self.get_total_questions(&session.exam_type);
            let completion_percentage = if total_questions > 0 {
                (total_answered as f64 / total_questions as f64) * 100.0
            } else {
                0.0
            };

            let progress = SessionProgressDto {
                completed_modules: 0, // TODO: 从模块进度表计算
                total_modules: self.get_total_modules(&session.exam_type),
                answered_questions: total_answered,
                total_questions,
                completion_percentage,
            };

            let incomplete_dto = IncompleteSessionDto {
                session_id: session.session_id.clone(),
                exam_type: session.exam_type,
                current_subject: session.current_subject,
                current_module_type: session.current_module_type,
                session_status: session.session_status,
                progress,
                created_at: session.created_at,
                last_activity: session.updated_at,
            };

            incomplete.push(incomplete_dto);
        }

        // 按最后活动时间倒序排列
        incomplete.sort_by(|a, b| b.last_activity.cmp(&a.last_activity));

        Ok(incomplete)
    }

    /// 计算用户统计信息
    async fn calculate_user_statistics(
        &self,
        exam_history: &[ExamHistoryDto],
        incomplete_sessions: &[IncompleteSessionDto],
    ) -> Result<UserExamStatisticsDto> {
        let total_exams = exam_history.len() + incomplete_sessions.len();
        let completed_exams = exam_history.len();
        let incomplete_exams = incomplete_sessions.len();

        // 计算平均分数和最高分数
        let scores: Vec<i32> = exam_history
            .iter()
            .filter_map(|h| h.total_score)
            .collect();

        let average_score = if !scores.is_empty() {
            Some(scores.iter().sum::<i32>() as f64 / scores.len() as f64)
        } else {
            None
        };

        let highest_score = scores.iter().max().copied();

        // 最近考试时间
        let last_exam_date = exam_history
            .iter()
            .filter_map(|h| h.completed_at)
            .max();

        // TODO: 计算总学习时间
        let total_study_time_seconds = exam_history
            .iter()
            .filter_map(|h| h.total_time_seconds)
            .sum::<i32>() as i64;

        Ok(UserExamStatisticsDto {
            total_exams: total_exams as i32,
            completed_exams: completed_exams as i32,
            incomplete_exams: incomplete_exams as i32,
            average_score,
            highest_score,
            last_exam_date,
            total_study_time_seconds,
        })
    }

    /// 获取考试类型对应的总模块数
    fn get_total_modules(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 4, // 语言2个模块 + 数学2个模块
            ExamType::Math => 2, // 数学2个模块
            ExamType::Reading => 2, // 语言2个模块
        }
    }

    /// 获取考试类型对应的总题数
    fn get_total_questions(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 98, // 语言54题 + 数学44题
            ExamType::Math => 44, // 数学44题
            ExamType::Reading => 54, // 语言54题
        }
    }
}
