//! 考试模块服务实现
//!
//! 负责处理考试模块的开始、题目获取等功能

use std::sync::Arc;
use tracing::{info, warn};

use crate::error::Result;
use crate::domain::exam::{
    ExamQuestionDomainService, ExamModuleProgressRepository, SatPaperRepository,
    ExamSessionRepository, ModuleStatus, ExamModuleProgress,
};

use super::super::dto::{
    StartModuleRequestDto, StartModuleResponseDto,
    ExamQuestionDto, ModuleInfoDto,
};

/// 模块服务实现
#[derive(Clone)]
pub struct ModuleServiceImpl {
    question_domain_service: Arc<ExamQuestionDomainService>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
    session_repository: Arc<dyn ExamSessionRepository>,
}

impl ModuleServiceImpl {
    pub fn new(
        question_domain_service: Arc<ExamQuestionDomainService>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
        session_repository: Arc<dyn ExamSessionRepository>,
    ) -> Self {
        Self {
            question_domain_service,
            progress_repository,
            paper_repository,
            session_repository,
        }
    }

    /// 开始模块
    pub async fn start_module(
        &self,
        request: StartModuleRequestDto,
    ) -> Result<StartModuleResponseDto> {
        info!("开始模块: 会话ID={}, 模块类型={}, 用户ID={}", 
              request.session_id, request.module_type, request.user_id);

        // 1. 获取会话信息
        let mut session = self.session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 验证用户权限
        if session.user_id != request.user_id {
            return Err(crate::error::Error::service("用户无权访问此会话"));
        }

        // 2. 获取模块题目
        let paper_id = session.paper_id;
        let exam_type = session.exam_type;
        let subject = match request.module_type {
            crate::domain::exam::ModuleType::Module1 => {
                // 第一模块根据当前学科确定
                session.current_subject
            },
            crate::domain::exam::ModuleType::Module2E | crate::domain::exam::ModuleType::Module2H => {
                // 第二模块也根据当前学科确定
                session.current_subject
            },
        };

        let questions = self.question_domain_service
            .get_module_questions(paper_id, exam_type, request.module_type, subject)
            .await?;

        // 3. 获取或创建模块进度记录（使用实际题目数量）
        let mut module_progress = match self.find_module_progress_with_subject(
            &request.session_id,
            request.module_type,
            subject
        ).await?
        {
            Some(mut existing_progress) => {
                info!("找到现有模块进度记录");
                // 更新题目数量以匹配实际题目
                existing_progress.total_questions = questions.len() as i32;
                // 如果模块还未开始，则开始模块
                if existing_progress.module_status == ModuleStatus::NotStarted {
                    existing_progress.start();
                    self.progress_repository.update(&existing_progress).await?;
                    info!("已开始模块: 会话ID={}, 模块类型={}", request.session_id, request.module_type);
                } else {
                    // 即使模块已开始，也要更新题目数量
                    self.progress_repository.update(&existing_progress).await?;
                }
                existing_progress
            },
            None => {
                // 如果没有进度记录，创建一个新的（使用实际题目数量）
                info!("创建新的模块进度记录");
                let mut new_progress = crate::domain::exam::ExamModuleProgress::new(
                    request.session_id.clone(),
                    request.user_id,
                    session.paper_id,
                    request.module_type,
                    subject,
                    questions.len() as i32, // 使用实际题目数量
                    request.module_type.time_limit_minutes(subject) * 60,
                );
                new_progress.start();
                self.progress_repository.create(&new_progress).await?;
                info!("已创建并开始模块: 会话ID={}, 模块类型={}, 题目数量={}",
                      request.session_id, request.module_type, questions.len());
                new_progress
            }
        };

        // 4. 初始化模块答题记录
        self.question_domain_service
            .initialize_module_answers(
                request.session_id.clone(),
                request.user_id,
                paper_id,
                &questions,
                request.module_type,
            )
            .await?;

        // 5. 获取题目详细内容并转换为DTO
        let question_dtos = self.convert_to_question_dtos_with_content(paper_id, &questions).await?;

        // 6. 更新会话状态 - 记录当前开始的模块
        session.start_module(request.module_type);
        self.session_repository.update(&session).await
            .map_err(|e| {
                warn!("更新会话状态失败: {}", e);
                crate::error::Error::service("更新会话状态失败，请稍后重试")
            })?;

        info!("已更新会话状态: 会话ID={}, 当前模块={:?}",
              request.session_id, session.current_module_type);

        // 7. 构建响应
        let response = StartModuleResponseDto {
            session_id: request.session_id.clone(),
            module_info: ModuleInfoDto {
                module_type: request.module_type,
                question_count: questions.len() as i32,
                time_limit_minutes: module_progress.time_limit_seconds / 60,
                difficulty_level: match request.module_type {
                    crate::domain::exam::ModuleType::Module1 => "standard".to_string(),
                    crate::domain::exam::ModuleType::Module2E => "easier".to_string(),
                    crate::domain::exam::ModuleType::Module2H => "harder".to_string(),
                },
                status: module_progress.module_status,
                adaptive_info: None,
            },
            questions: question_dtos,
            answered_questions: vec![], // 刚开始时没有已答题目
            remaining_time_seconds: module_progress.remaining_time_seconds.unwrap_or(module_progress.time_limit_seconds),
            current_question_index: 1, // 从第一题开始
        };

        info!("模块开始成功: 会话ID={}, 模块类型={}, 题目数量={}", 
              request.session_id, request.module_type, response.questions.len());
        Ok(response)
    }

    /// 转换题目内容为DTO（包含详细内容）
    async fn convert_to_question_dtos_with_content(
        &self,
        paper_id: i64,
        paper_questions: &[crate::domain::exam::SatPaperQuestion],
    ) -> Result<Vec<ExamQuestionDto>> {
        info!("获取题目详细内容: paper_id={}, 题目数量={}", paper_id, paper_questions.len());

        // 获取试卷的所有题目详细内容（考试时不包含答案）
        let question_details = self.paper_repository
            .find_question_contents_by_paper(paper_id, false) // 考试时不返回答案
            .await?;

        // 创建题目ID到详细内容的映射
        let mut question_detail_map = std::collections::HashMap::new();
        for detail in question_details {
            question_detail_map.insert(detail.question_id, detail);
        }

        // 转换为DTO，按module_sequence排序
        let mut question_dtos = Vec::new();
        for paper_q in paper_questions {
            if let Some(detail) = question_detail_map.get(&paper_q.question_id) {
                question_dtos.push(ExamQuestionDto::from_question_detail(
                    detail,
                    paper_q.module_sequence,
                    false, // 考试时不包含答案
                ));
            } else {
                // 如果找不到详细内容，创建基础信息
                info!("未找到题目详细内容: question_id={}", paper_q.question_id);
                question_dtos.push(ExamQuestionDto {
                    id: paper_q.question_id,
                    subject_id: paper_q.subject_id,
                    knowledge_id: 0,
                    type_id: 0,
                    difficulty: 0.0,
                    content: serde_json::Value::Null,
                    options: None,
                    answer: None,
                    explanation: None,
                    elo_rating: 0.0,
                    usage_count: 0,
                    correct_count: 0,
                    question_set: None,
                    url: None,
                    is_active: true,
                    created_at: None,
                    updated_at: None,
                    module_sequence: paper_q.module_sequence,
                });
            }
        }

        // 按模块序号排序
        question_dtos.sort_by_key(|q| q.module_sequence);

        // 按模块内顺序排序
        question_dtos.sort_by_key(|q| q.module_sequence);
        info!("题目内容转换完成: 题目数量={}", question_dtos.len());
        Ok(question_dtos)
    }

    /// 查找模块进度记录（使用学科信息）
    async fn find_module_progress_with_subject(
        &self,
        session_id: &str,
        module_type: crate::domain::exam::ModuleType,
        subject: crate::domain::exam::Subject,
    ) -> Result<Option<crate::domain::exam::ExamModuleProgress>> {
        // 查询该会话的所有模块进度记录
        let all_progress = self.progress_repository
            .find_by_session_id(session_id)
            .await?;

        // 在内存中查找匹配的记录（同时匹配学科和模块类型）
        let matching_progress = all_progress.into_iter()
            .find(|p| p.subject == subject && p.module_type == module_type);

        Ok(matching_progress)
    }
}
