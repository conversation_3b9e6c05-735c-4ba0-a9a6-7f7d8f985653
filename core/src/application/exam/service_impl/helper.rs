//! 考试辅助服务实现
//!
//! 提供通用的辅助功能，如数据转换、验证等

use crate::domain::exam::{ExamSession, ExamModuleProgress};
use super::super::dto::{ExamSessionDto, ModuleProgressDto, ExamQuestionDto};

/// 辅助服务实现
pub struct HelperServiceImpl;

impl HelperServiceImpl {
    pub fn new() -> Self {
        Self
    }

    /// 转换会话实体为DTO
    pub fn convert_to_session_dto(&self, session: ExamSession) -> ExamSessionDto {
        ExamSessionDto {
            session_id: session.session_id,
            user_id: session.user_id,
            paper_id: session.paper_id,
            exam_type: session.exam_type,
            current_subject: session.current_subject,
            current_module_type: session.current_module_type,
            session_status: session.session_status,
            total_time_seconds: session.total_time_seconds,
            reading_time_seconds: session.reading_time_seconds,
            math_time_seconds: session.math_time_seconds,
            created_at: session.created_at,
            updated_at: session.updated_at,
            completed_at: session.completed_at,
        }
    }

    /// 转换模块进度实体为DTO
    pub fn convert_to_progress_dto(&self, progress: &ExamModuleProgress) -> ModuleProgressDto {
        ModuleProgressDto {
            module_type: progress.module_type,
            subject: progress.subject,
            subject_name: progress.subject.name().to_string(),
            total_questions: progress.total_questions,
            answered_questions: progress.answered_questions,
            correct_questions: progress.correct_questions,
            time_limit_seconds: progress.time_limit_seconds,
            time_used_seconds: progress.time_used_seconds,
            remaining_time_seconds: progress.remaining_time_seconds,
            module_status: progress.module_status,
            started_at: progress.started_at,
            completed_at: progress.completed_at,
        }
    }

    /// 转换题目内容为DTO
    /// TODO: 实现具体的题目内容转换逻辑
    pub fn convert_to_question_dtos(
        &self,
        _contents: Vec<()>, // 暂时使用空类型，待实现
        paper_questions: &[crate::domain::exam::SatPaperQuestion],
    ) -> Vec<ExamQuestionDto> {
        // 暂时返回基础的题目信息，不包含具体内容
        let mut question_dtos = Vec::new();

        for paper_q in paper_questions {
            question_dtos.push(ExamQuestionDto {
                id: paper_q.question_id,
                subject_id: paper_q.subject_id,
                knowledge_id: 0, // TODO: 从题目服务获取
                type_id: 0, // TODO: 从题目服务获取
                difficulty: 0.0,
                content: serde_json::Value::Null, // TODO: 从题目服务获取
                options: None, // TODO: 从题目服务获取
                answer: None, // 考试时不返回答案
                explanation: None, // 考试时不返回解析
                elo_rating: 0.0, // TODO: 从题目服务获取
                usage_count: 0,
                correct_count: 0,
                question_set: None,
                url: None,
                is_active: true,
                created_at: None,
                updated_at: None,
                module_sequence: paper_q.module_sequence,
            });
        }

        // 按模块内顺序排序
        question_dtos.sort_by_key(|q| q.module_sequence);
        question_dtos
    }

    /// 验证用户ID格式
    pub fn validate_user_id(&self, user_id: i64) -> bool {
        user_id > 0
    }

    /// 验证会话ID格式
    pub fn validate_session_id(&self, session_id: &str) -> bool {
        !session_id.is_empty() && session_id.len() <= 100
    }

    /// 验证试卷ID格式
    pub fn validate_paper_id(&self, paper_id: i64) -> bool {
        paper_id > 0
    }

    /// 生成会话ID
    pub fn generate_session_id(&self, user_id: i64, paper_id: i64) -> String {
        use chrono::Utc;
        let timestamp = Utc::now().timestamp_millis();
        format!("exam_{}_{}__{}", user_id, paper_id, timestamp)
    }

    /// 计算剩余时间
    pub fn calculate_remaining_time(
        &self,
        time_limit_seconds: i32,
        time_used_seconds: i32,
    ) -> Option<i32> {
        let remaining = time_limit_seconds - time_used_seconds;
        if remaining > 0 {
            Some(remaining)
        } else {
            Some(0) // 时间用完但不返回负数
        }
    }

    /// 计算完成率
    pub fn calculate_completion_rate(
        &self,
        answered_count: i32,
        total_count: i32,
    ) -> f64 {
        if total_count > 0 {
            answered_count as f64 / total_count as f64
        } else {
            0.0
        }
    }

    /// 计算正确率
    pub fn calculate_accuracy_rate(
        &self,
        correct_count: i32,
        answered_count: i32,
    ) -> f64 {
        if answered_count > 0 {
            correct_count as f64 / answered_count as f64
        } else {
            0.0
        }
    }

    /// 格式化时间显示
    pub fn format_duration_seconds(&self, seconds: i32) -> String {
        let hours = seconds / 3600;
        let minutes = (seconds % 3600) / 60;
        let secs = seconds % 60;

        if hours > 0 {
            format!("{}小时{}分钟{}秒", hours, minutes, secs)
        } else if minutes > 0 {
            format!("{}分钟{}秒", minutes, secs)
        } else {
            format!("{}秒", secs)
        }
    }

    /// 验证答案格式
    pub fn validate_answer_format(&self, answer: &str) -> bool {
        // SAT答案通常是A、B、C、D或数字
        if answer.is_empty() {
            return false;
        }

        let trimmed = answer.trim().to_uppercase();
        
        // 选择题答案：A、B、C、D
        if ["A", "B", "C", "D"].contains(&trimmed.as_str()) {
            return true;
        }

        // 数字答案（填空题）
        if trimmed.parse::<f64>().is_ok() {
            return true;
        }

        // 分数答案（如 1/2）
        if trimmed.contains('/') {
            let parts: Vec<&str> = trimmed.split('/').collect();
            if parts.len() == 2 {
                return parts[0].parse::<i32>().is_ok() && parts[1].parse::<i32>().is_ok();
            }
        }

        false
    }
}
