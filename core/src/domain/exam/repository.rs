//! 考试仓储接口
//!
//! 定义考试系统的数据访问接口，遵循DDD架构原则

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use crate::error::Result;

use super::entities::{ExamSession, ExamAnswer, ExamModuleProgress};
use super::value_objects::{ExamType, ModuleType, Subject};

/// 考试会话仓储接口
#[async_trait]
pub trait ExamSessionRepository: Send + Sync {
    /// 创建考试会话
    async fn create(&self, session: &ExamSession) -> Result<()>;

    /// 根据会话ID查找会话
    async fn find_by_session_id(&self, session_id: &str) -> Result<Option<ExamSession>>;

    /// 根据用户ID查找进行中的会话
    async fn find_active_by_user_id(&self, user_id: i64) -> Result<Option<ExamSession>>;

    /// 更新会话
    async fn update(&self, session: &ExamSession) -> Result<()>;

    /// 根据用户ID获取历史会话列表
    async fn find_history_by_user_id(&self, user_id: i64, limit: Option<i32>) -> Result<Vec<ExamSession>>;
}

/// 考试答题记录仓储接口
#[async_trait]
pub trait ExamAnswerRepository: Send + Sync {
    /// 创建答题记录
    async fn create(&self, answer: &ExamAnswer) -> Result<()>;

    /// 批量创建答题记录
    async fn batch_create(&self, answers: &[ExamAnswer]) -> Result<()>;

    /// 更新答题记录
    async fn update_or_create(&self, answer: &ExamAnswer) -> Result<()>;

    /// 根据会话ID查找所有答题记录
    async fn find_by_session_id(&self, session_id: &str) -> Result<Vec<ExamAnswer>>;

    /// 根据会话ID和题目ID查找答题记录
    async fn find_by_session_and_question(
        &self,
        session_id: &str,
        question_id: i32,
    ) -> Result<Option<ExamAnswer>>;

    /// 根据会话ID和模块类型查找答题记录
    async fn find_by_session_and_module(
        &self,
        session_id: &str,
        module_type: ModuleType,
    ) -> Result<Vec<ExamAnswer>>;

    /// 统计会话的答题情况
    async fn count_answers_by_session(&self, session_id: &str) -> Result<(i32, i32, i32)>; // (总数, 已答, 正确)
}

/// 考试模块进度仓储接口
#[async_trait]
pub trait ExamModuleProgressRepository: Send + Sync {
    /// 创建模块进度
    async fn create(&self, progress: &ExamModuleProgress) -> Result<()>;

    /// 批量创建模块进度
    async fn batch_create(&self, progresses: &[ExamModuleProgress]) -> Result<()>;

    /// 更新模块进度
    async fn update(&self, progress: &ExamModuleProgress) -> Result<()>;

    /// 根据会话ID查找所有模块进度
    async fn find_by_session_id(&self, session_id: &str) -> Result<Vec<ExamModuleProgress>>;

    /// 根据会话ID和模块类型查找模块进度
    async fn find_by_session_and_module(
        &self,
        session_id: &str,
        module_type: ModuleType,
    ) -> Result<Option<ExamModuleProgress>>;

    /// 根据会话ID和学科查找模块进度
    async fn find_by_session_and_subject(
        &self,
        session_id: &str,
        subject: Subject,
    ) -> Result<Vec<ExamModuleProgress>>;
}

/// SAT试卷仓储接口
/// 
/// 用于访问现有的试卷数据，复用 t_sat_paper 和 t_sat_paper_question 表
#[async_trait]
pub trait SatPaperRepository: Send + Sync {
    /// 根据试卷ID获取试卷信息
    async fn find_paper_by_id(&self, paper_id: i64) -> Result<Option<SatPaper>>;

    /// 根据试卷ID和模块类型获取题目列表
    async fn find_questions_by_paper_and_module(
        &self,
        paper_id: i64,
        module_type: ModuleType,
    ) -> Result<Vec<SatPaperQuestion>>;

    /// 根据试卷ID获取所有题目
    async fn find_all_questions_by_paper(&self, paper_id: i64) -> Result<Vec<SatPaperQuestion>>;

    /// 根据考试类型筛选题目
    async fn find_questions_by_paper_and_exam_type(
        &self,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Vec<SatPaperQuestion>>;

    /// 根据试卷ID获取题目内容详情
    ///
    /// 获取试卷中所有题目的完整内容，包括题目内容、选项、答案等
    /// 注意：考试时不应返回答案和解析
    async fn find_question_contents_by_paper(
        &self,
        paper_id: i64,
        include_answers: bool,
    ) -> Result<Vec<QuestionContentDetail>>;
}

/// SAT试卷实体（对应 t_sat_paper 表）
#[derive(Debug, Clone)]
pub struct SatPaper {
    pub paper_id: i64,
    pub paper_name: String,
    pub paper_inner_name: String,
    pub version: i32,
    pub status: i32,
    pub subject_id: Option<i32>,
}

/// SAT试卷题目实体（对应 t_sat_paper_question 表）
#[derive(Debug, Clone)]
pub struct SatPaperQuestion {
    pub paper_id: i64,
    pub question_id: i32,
    pub module_type: String,
    pub module_sequence: i32,
    pub subject_id: i32,
    pub subject_group_id: i32,
}

impl SatPaperQuestion {
    /// 获取模块类型
    pub fn get_module_type(&self) -> Option<ModuleType> {
        ModuleType::from_str(&self.module_type)
    }

    /// 获取学科
    pub fn get_subject(&self) -> Subject {
        match self.subject_id {
            1 => Subject::Math,
            14 => Subject::Reading,
            _ => Subject::Reading, // 默认为语言
        }
    }
}

/// 题目内容详情（对应 t_sat_question 表的完整信息）
#[derive(Debug, Clone)]
pub struct QuestionContentDetail {
    pub question_id: i32,
    pub subject_id: i32,
    pub knowledge_id: i32,
    pub type_id: i32,
    pub difficulty: i16,
    pub question_content: serde_json::Value,
    pub options: Option<serde_json::Value>,
    pub answer: serde_json::Value,
    pub explanation: Option<serde_json::Value>,
    pub elo_rating: f64,
    pub irt_difficulty: Option<f64>,
    pub irt_discrimination: Option<f64>,
    pub irt_guessing: Option<f64>,
    pub usage_count: i32,
    pub correct_count: i32,
    pub question_set: Option<String>,
    pub url: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub section_id: Option<i32>,
    pub status: Option<i32>,
    // 试卷相关信息
    pub module_type: String,
    pub module_sequence: i32,
    pub subject_group_id: i32,
}

// QuestionContent 相关定义已移至 question 领域模块
